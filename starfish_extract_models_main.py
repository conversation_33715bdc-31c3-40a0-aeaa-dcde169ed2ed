import os
import sys
import pandas as pd
import json
from datetime import datetime

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(BASE_DIR)

from src.model_llm import ModelLLM


def load_data(file_path):
    """加载Excel数据"""
    try:
        df = pd.read_excel(file_path)
        print(f"成功加载数据，共 {len(df)} 条记录")
        print(f"列名: {df.columns.tolist()}")
        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None


def process_results(log_file_path, original_df):
    """处理LLM响应结果，合并到原始数据"""
    results = []
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    result = json.loads(line.strip())
                    results.append(result)
        
        # 创建结果DataFrame
        results_df = pd.DataFrame(results)
        
        # 合并原始数据和结果
        merged_df = original_df.merge(results_df, left_on='ASIN', right_on='asin', how='left')
        
        # 重命名列
        merged_df = merged_df.rename(columns={
            'extracted_printer_models': 'Extracted_Printer_Models',
            'response': 'LLM_Response'
        })
        
        # 删除重复的asin列
        if 'asin' in merged_df.columns:
            merged_df = merged_df.drop(columns=['asin'])
        
        return merged_df
    
    except Exception as e:
        print(f"处理结果失败: {e}")
        return None


def main():
    pt = "vacuum_cleaner"
    # 文件路径
    input_file = f"{BASE_DIR}/data/{pt.upper()}.xlsx"
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"{BASE_DIR}/output/{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    log_file = f"{output_dir}/{pt}_model_extraction_log.json"
    output_excel = f"{output_dir}/{pt}_model_extraction_results.xlsx"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"输入文件不存在: {input_file}")
        return
    
    print("=== 打印机型号提取程序 ===")
    print(f"输入文件: {input_file}")
    print(f"输出目录: {output_dir}")
    
    # 加载数据
    df = load_data(input_file)
    if df is None:
        return
    
    # 检查必要的列
    required_columns = ['ASIN', 'Input Text']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"缺少必要的列: {missing_columns}")
        return
    
    # 过滤掉没有Input Text的行
    df_filtered = df[df['Input Text'].notna() & (df['Input Text'] != '')].copy()
    print(f"过滤后的数据行数: {len(df_filtered)}")
    
    if len(df_filtered) == 0:
        print("没有有效的输入文本数据")
        return
    
    # 初始化LLM
    print("\n初始化LLM...")
    llm = ModelLLM(model_name="Claude 3.7 Sonnet")
    
    # 处理数据
    print("\n开始处理数据...")
    llm.multi_thread_run(df_filtered, log_file)
    
    # 处理结果
    print("\n处理结果...")
    final_df = process_results(log_file, df)
    
    if final_df is not None:
        # 保存结果
        final_df.to_excel(output_excel, index=False)
        print(f"\n结果已保存到: {output_excel}")
        
        # 显示统计信息
        print("\n=== 处理结果统计 ===")
        print(f"总记录数: {len(final_df)}")
        
        if 'Extracted_Printer_Models' in final_df.columns:
            # 统计成功提取的记录数
            valid_extractions = final_df[
                (final_df['Extracted_Printer_Models'].notna()) & 
                (final_df['Extracted_Printer_Models'] != 'No relevant information') &
                (final_df['Extracted_Printer_Models'] != '无相关信息') &
                (final_df['Extracted_Printer_Models'] != 'Parsing failed') &
                (final_df['Extracted_Printer_Models'] != '解析失败')
            ]
            print(f"成功提取打印机型号信息的记录数: {len(valid_extractions)}")
            print(f"成功率: {len(valid_extractions) / len(df_filtered) * 100:.1f}%")
            
            # 显示几个示例
            print("\n=== 提取示例 ===")
            for idx, row in valid_extractions.head(3).iterrows():
                print(f"\nASIN: {row['ASIN']}")
                print(f"提取的打印机型号信息: {row['Extracted_Printer_Models']}")
    else:
        print("结果处理失败")
    
    print("\n程序执行完成!")


if __name__ == "__main__":
    main()