# 打印机型号提取项目

## 项目概述

基于原有的产品兼容性验证系统，改写实现了一个全新的功能：从商品描述信息中提取打印机型号相关的原文句子。

## 功能说明

该程序能够：
1. 读取Excel文件中的商品ASIN和描述信息（Input Text列）
2. 使用大语言模型分析每个商品的描述信息
3. 从描述信息中提取所有提到打印机型号的原文句子
4. 生成包含原始数据和提取结果的新Excel文件

## 文件结构

### 核心文件
- `extract_printer_models_main.py` - 主程序，处理完整数据集
- `demo_extraction.py` - 演示程序，可选择处理记录数量
- `test_extraction.py` - 测试程序，验证功能正确性

### 支持模块
- `src/extract_printer_models.py` - 提示模板和响应解析
- `src/printer_model_llm.py` - LLM调用逻辑
- `src/constant.py` - 模型配置（复用原有）

## 使用方法

### 1. 完整运行
```bash
python extract_printer_models_main.py
```
处理所有数据，生成完整的提取结果。

### 2. 演示模式
```bash
python demo_extraction.py
```
提供三种选择：
- 演示模式（10条记录）
- 小规模测试（50条记录）
- 完整运行（所有记录）

### 3. 功能测试
```bash
python test_extraction.py
```
运行各项功能测试，验证系统工作正常。

## 输出说明

### Excel文件结构
生成的Excel文件包含以下列：
- **原始列**: ASIN, MP, Product Type, Attribute, Input Text, Output Text, Remarks, associate_alias
- **新增列**: 
  - `Extracted_Printer_Models`: 提取的打印机型号相关原文句子
  - `LLM_Response`: 原始LLM响应（可选）

### 输出目录
- `output/YYYYMMDD_HHMMSS/` - 按时间戳命名的输出目录
- `*_extraction_results.xlsx` - 提取结果Excel文件
- `*_extraction_log.json` - JSON格式的处理日志

## 提取规则

程序会从产品描述中提取以下类型的句子：
1. 提到具体打印机型号的句子（如：HP OfficeJet 8010, Canon PIXMA TS3120）
2. 提到打印机系列的句子（如：HP OfficeJet Pro 8020 Series）
3. 提到兼容打印机的句子（包含"Compatible"、"Works with"等关键词）
4. 原文完整句子，不进行修改或总结

## 示例结果

**ASIN**: B07N3G1PYM
**提取结果**: "Works with HP OfficeJet 8010, 8020 Series, HP OfficeJet Pro 8020, 8030 Series | This cartridge works with: HP OfficeJet 8010, 8010e, 8012e, 8014e, 8015, 8015e, 8018, 8022, 8022e"

**ASIN**: B0CDKHJRYQ  
**提取结果**: "Compatible Printers: ENVY Photo 7855 7858 7864 7155 7158 7164 7120 7130 7132 7134 7800 7820 7822 7830"

## 技术特点

1. **多线程处理**: 使用ThreadPoolExecutor实现并发处理
2. **AWS Bedrock集成**: 支持多区域Claude模型调用
3. **错误处理**: 完善的异常处理和重试机制
4. **进度显示**: 使用tqdm显示处理进度
5. **结果统计**: 自动计算提取成功率和统计信息

## 依赖要求

- Python 3.x
- pandas: Excel文件读写
- boto3: AWS服务调用
- tqdm: 进度条显示
- 需要配置AWS凭证，具备Bedrock和DynamoDB访问权限

## 配置说明

系统使用以下AWS服务：
- **AWS Bedrock**: Claude模型调用
- **DynamoDB**: 账户信息管理（表名：claude_account）

确保AWS Profile 'compatibility' 已正确配置。