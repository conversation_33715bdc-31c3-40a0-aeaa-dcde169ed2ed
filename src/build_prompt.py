def get_comp_criterion(pt_pair):
    comp_criterion = {
        "CAMERA_DIGITAL-FLASH_MEMORY": {
            "primary_pt": "normal camera",
            "accessory_pt": "flash memory card",
            "primary_definition": "Digital camera devices for capturing still photos and videos",
            "accessory_definition": "Memory storage devices that can be inserted into electronic devices",
            "primary_includes": [
                "Digital SLR cameras (DSLR)",
                "Mirrorless cameras", 
                "Point-and-shoot digital cameras",
                "Action/sports cameras",
                "Professional camcorders"
            ],
            "primary_excludes": [
                "Smartphones (although they have camera functions)",
                "Tablets",
                "Security cameras",
                "Webcams",
                "Camera accessories (lenses, tripods, etc.)"
            ],
            "accessory_includes": [
                "SD cards",
                "SDHC cards", 
                "SDXC cards",
                "MicroSD cards",
                "CompactFlash cards",
                "Memory sticks"
            ],
            "accessory_excludes": [
                "Internal hard drives",
                "USB flash drives for computer use only",
                "Card readers",
                "Memory card cases"
            ],
            "primary_compatibility_criterion": "Normal cameras that can accept and use various types of flash memory cards for storing photos and videos",
            "accessory_compatibility_criterion": "Any type of SD card or flash memory that can be inserted into cameras for storing photos and videos"
        },
        "CELLULAR_PHONE-FLASH_MEMORY": {
            "primary_pt": "cell phone",
            "accessory_pt": "flash memory card",
            "primary_definition": "Mobile communication devices supporting cellular network calls and data transmission",
            "accessory_definition": "Memory storage devices that can be inserted into electronic devices",
            "primary_includes": [
                "Smartphones (iPhone, Android phones, etc.)",
                "Feature phones/basic phones",
                "Rugged phones",
                "Gaming phones"
            ],
            "primary_excludes": [
                "Landline phones",
                "Tablets",
                "Smart watches",
                "Phone accessories (cases, chargers, etc.)"
            ],
            "accessory_includes": [
                "MicroSD cards",
                "SD cards",
                "SDHC cards",
                "SDXC cards"
            ],
            "accessory_excludes": [
                "Internal phone storage",
                "USB flash drives",
                "Cloud storage services",
                "Card readers"
            ],
            "primary_compatibility_criterion": "Cell phones that have expandable storage capabilities and can accept flash memory cards for additional storage",
            "accessory_compatibility_criterion": "Any type of SD card which can be inserted into cell phones for expanding storage capacity"
        },
        "CELLULAR_PHONE-HEADPHONES": {
            "primary_pt": "cell phone",
            "accessory_pt": "headphones or earphones",
            "primary_definition": "Mobile communication devices supporting cellular network calls and data transmission",
            "accessory_definition": "Audio devices worn on or in the ears for personal listening",
            "primary_includes": [
                "Smartphones (iPhone, Android phones, etc.)",
                "Feature phones/basic phones",
                "Rugged phones",
                "Gaming phones"
            ],
            "primary_excludes": [
                "Landline phones",
                "Tablets",
                "Smart watches",
                "Phone accessories (cases, chargers, etc.)"
            ],
            "accessory_includes": [
                "Wired headphones with 3.5mm jack",
                "USB-C headphones",
                "Lightning headphones",
                "Bluetooth wireless headphones",
                "True wireless earbuds",
                "Gaming headsets with microphone"
            ],
            "accessory_excludes": [
                "Speakers (external audio devices)",
                "Hearing aids",
                "Microphones (standalone recording devices)",
                "Audio interfaces"
            ],
            "primary_compatibility_criterion": "Cell phones that can connect to and support headphones or earphones for audio output through various connection methods",
            "accessory_compatibility_criterion": "Headphones or earphones that can connect to cell phones via audio jack, USB port, or Bluetooth"
        },
        "VIDEO_GAME_CONSOLE-FLASH_MEMORY": {
            "primary_pt": "video game console",
            "accessory_pt": "flash memory card",
            "primary_definition": "Dedicated gaming systems designed primarily for playing video games",
            "accessory_definition": "Memory storage devices that can be inserted into electronic devices",
            "primary_includes": [
                "Home game consoles (PlayStation, Xbox, Nintendo Switch, etc.)",
                "Handheld gaming consoles (Nintendo 3DS, Steam Deck, etc.)",
                "Retro gaming consoles"
            ],
            "primary_excludes": [
                "Gaming PCs/computers",
                "Gaming laptops",
                "Smartphones with gaming capabilities",
                "Game controllers (accessories)",
                "Game software/cartridges"
            ],
            "accessory_includes": [
                "SD cards",
                "MicroSD cards", 
                "SDHC cards",
                "SDXC cards",
                "Console-specific memory cards"
            ],
            "accessory_excludes": [
                "Internal console storage",
                "External hard drives",
                "USB flash drives for PC gaming",
                "Game cartridges"
            ],
            "primary_compatibility_criterion": "Video game consoles that support expandable storage and can accept flash memory cards for game storage or save data",
            "accessory_compatibility_criterion": "Any type of SD card or flash memory that can be used in video game consoles for game storage or save data"
        },
        "NOTEBOOK_COMPUTER-COMPUTER_DRIVE_OR_STORAGE": {
            "primary_pt": "notebook computer",
            "accessory_pt": "computer drive or storage",
            "primary_definition": "Portable personal computers with integrated screen, keyboard, and trackpad",
            "accessory_definition": "Storage devices designed for installation in computers",
            "primary_includes": [
                "Traditional laptops (Windows/Mac/Linux systems)",
                "Gaming laptops",
                "Business laptops",
                "Ultrabooks/thin laptops",
                "2-in-1 laptops"
            ],
            "primary_excludes": [
                "Pure tablets (without physical keyboards)",
                "Desktop computers",
                "Servers",
                "Laptop accessories (chargers, bags, etc.)"
            ],
            "accessory_includes": [
                "Internal 2.5-inch SATA SSDs",
                "Internal 2.5-inch SATA HDDs",
                "M.2 NVMe SSDs",
                "M.2 SATA SSDs"
            ],
            "accessory_excludes": [
                "External USB drives",
                "SD cards",
                "3.5-inch desktop drives",
                "Network attached storage"
            ],
            "primary_compatibility_criterion": "Notebook computers that have internal storage bays and can accommodate additional or replacement internal drives",
            "accessory_compatibility_criterion": "Internal hard drives or SSDs that can be installed inside notebook computers"
        },
        "PERSONAL_COMPUTER-COMPUTER_DRIVE_OR_STORAGE": {
            "primary_pt": "personal computer",
            "accessory_pt": "computer drive or storage",
            "primary_definition": "Desktop personal computer systems",
            "accessory_definition": "Storage devices designed for installation in computers",
            "primary_includes": [
                "Desktop computers (towers)",
                "All-in-one computers",
                "Workstations",
                "Gaming desktops",
                "Mini PCs/NUCs"
            ],
            "primary_excludes": [
                "Laptops",
                "Tablets",
                "Servers",
                "Computer accessories (monitors, keyboards, etc.)"
            ],
            "accessory_includes": [
                "Internal 3.5-inch SATA HDDs",
                "Internal 2.5-inch SATA SSDs",
                "M.2 NVMe SSDs",
                "M.2 SATA SSDs"
            ],
            "accessory_excludes": [
                "External USB drives",
                "SD cards",
                "Network attached storage",
                "Optical drives"
            ],
            "primary_compatibility_criterion": "Personal computers that have internal storage bays and can accommodate additional or replacement internal drives",
            "accessory_compatibility_criterion": "Internal hard drives or SSDs that can be installed inside personal computers"
        },
        "TELEVISION-HEADPHONES": {
            "primary_pt": "television",
            "accessory_pt": "headphones or earphones",
            "primary_definition": "Display devices for receiving and showing TV signals or video content",
            "accessory_definition": "Audio devices worn on or in the ears for personal listening",
            "primary_includes": [
                "LCD/LED TVs",
                "OLED TVs",
                "QLED TVs",
                "Smart TVs",
                "4K/8K TVs"
            ],
            "primary_excludes": [
                "Computer monitors",
                "Tablets",
                "Smartphones",
                "TV accessories (remotes, mounts, etc.)"
            ],
            "accessory_includes": [
                "Wired headphones with audio jack",
                "Bluetooth wireless headphones",
                "TV-specific wireless headphones",
                "RF wireless headphones"
            ],
            "accessory_excludes": [
                "Speakers (external audio devices)",
                "Hearing aids",
                "USB headphones (computer-specific)",
                "Lightning headphones (phone-specific)"
            ],
            "primary_compatibility_criterion": "Televisions that have audio output capabilities and can connect to headphones or earphones for private listening",
            "accessory_compatibility_criterion": "Headphones or earphones that can connect to televisions via audio jack or Bluetooth"
        },
        "CELLULAR_PHONE-CELLULAR_PHONE_CASE": {
            "primary_pt": "cell phone",
            "accessory_pt": "cellular phone case",
            "primary_definition": "Mobile communication devices supporting cellular network calls and data transmission",
            "accessory_definition": "Protective covers and cases specifically designed for mobile phones",
            "primary_includes": [
                "Smartphones (iPhone, Android phones, etc.)",
                "Feature phones/basic phones",
                "Rugged phones",
                "Gaming phones"
            ],
            "primary_excludes": [
                "Landline phones",
                "Tablets",
                "Smart watches",
                "Phone accessories (chargers, screen protectors, etc.)"
            ],
            "accessory_includes": [
                "Hard shell phone cases",
                "Soft silicone phone cases",
                "Leather phone cases",
                "Wallet phone cases",
                "Rugged/heavy-duty phone cases",
                "Clear/transparent phone cases",
                "Battery phone cases (with built-in battery)",
                "Waterproof phone cases"
            ],
            "accessory_excludes": [
                "Screen protectors",
                "Phone chargers and cables",
                "Phone mounts and stands (unless integrated with case)",
                "Generic pouches not designed for specific phone models",
                "Tablet cases",
                "Laptop cases"
            ],
            "primary_compatibility_criterion": "Cell phones that can be protected and enhanced by specifically designed phone cases with proper fit and access to all functions",
            "accessory_compatibility_criterion": "Phone cases that are specifically designed and sized to fit particular cell phone models, with proper cutouts for buttons, ports, and cameras"
        },
        "CELLULAR_PHONE-SCREEN_PROTECTOR": {
            "primary_pt": "cell phone",
            "accessory_pt": "screen protector",
            "primary_definition": "Mobile communication devices supporting cellular network calls and data transmission",
            "accessory_definition": "Protective films or glass covers designed to protect device screens from scratches and damage",
            "primary_includes": [
                "Smartphones (iPhone, Android phones, etc.)",
                "Feature phones/basic phones",
                "Rugged phones",
                "Gaming phones"
            ],
            "primary_excludes": [
                "Landline phones",
                "Tablets",
                "Smart watches",
                "Phone cases and other accessories"
            ],
            "accessory_includes": [
                "Tempered glass screen protectors",
                "Plastic/film screen protectors",
                "Privacy screen protectors",
                "Anti-glare screen protectors",
                "Blue light blocking screen protectors",
                "Curved screen protectors (for curved displays)"
            ],
            "accessory_excludes": [
                "Phone cases",
                "Tablet screen protectors",
                "Computer monitor screen protectors",
                "Camera lens protectors",
                "Generic protective films not sized for phones"
            ],
            "primary_compatibility_criterion": "Cell phones with screens that can be protected by precisely fitted screen protectors without interfering with touch sensitivity or display quality",
            "accessory_compatibility_criterion": "Screen protectors that are precisely cut and sized to fit the specific dimensions and screen characteristics of particular cell phone models"
        },
        "PRINTER-INKJET_PRINTER_INK": {
            "primary_pt": "printer",
            "accessory_pt": "inkjet printer ink",
            "primary_definition": "Printing devices that produce hard copies of digital documents or images",
            "accessory_definition": "Liquid ink cartridges or bottles specifically designed for inkjet printing technology",
            "primary_includes": [
                "Inkjet printers",
                "All-in-one inkjet printers",
                "Photo inkjet printers",
                "Wide-format inkjet printers",
                "Portable inkjet printers"
            ],
            "primary_excludes": [
                "Laser printers",
                "Dot matrix printers",
                "3D printers",
                "Thermal printers",
                "Printer accessories (paper, cables, etc.)"
            ],
            "accessory_includes": [
                "Original manufacturer ink cartridges",
                "Compatible/third-party ink cartridges",
                "Refillable ink cartridges",
                "Ink bottles for tank printers",
                "Multi-color ink cartridges",
                "Individual color ink cartridges"
            ],
            "accessory_excludes": [
                "Laser printer toner cartridges",
                "Printer paper",
                "Ribbon cartridges",
                "3D printer filaments",
                "Thermal printer paper"
            ],
            "primary_compatibility_criterion": "Inkjet printers that use liquid ink delivery systems and can accept specific ink cartridges or ink bottles for printing",
            "accessory_compatibility_criterion": "Ink cartridges or ink bottles that are specifically designed and manufactured to work with particular inkjet printer models and their ink delivery systems"
        },
        "CAMERA_DIGITAL-CAMERA_LENSES": {
            "primary_pt": "normal camera",
            "accessory_pt": "camera lenses",
            "primary_definition": "Digital camera devices for capturing still photos and videos",
            "accessory_definition": "Optical lenses designed to attach to cameras for various photographic purposes",
            "primary_includes": [
                "Digital SLR cameras (DSLR)",
                "Mirrorless cameras",
                "Professional cameras with interchangeable lens systems"
            ],
            "primary_excludes": [
                "Point-and-shoot cameras with fixed lenses",
                "Smartphones",
                "Action cameras with fixed lenses",
                "Security cameras",
                "Webcams"
            ],
            "accessory_includes": [
                "Prime lenses (fixed focal length)",
                "Zoom lenses (variable focal length)",
                "Wide-angle lenses",
                "Telephoto lenses",
                "Macro lenses",
                "Portrait lenses",
                "Fish-eye lenses"
            ],
            "accessory_excludes": [
                "Smartphone camera lens attachments",
                "Lens filters",
                "Lens caps and covers",
                "Tripods",
                "Camera bodies",
                "Flash units"
            ],
            "primary_compatibility_criterion": "Digital cameras with interchangeable lens systems that can accept and use various types of camera lenses with matching mount types",
            "accessory_compatibility_criterion": "Camera lenses that have the correct mount type (Canon EF, Nikon F, Sony E, etc.) and are mechanically and electronically compatible with the specific camera system"
        },
        "VIDEO_GAME_CONSOLE-ELECTRONIC_DEVICE_SKIN": {
            "primary_pt": "video game console",
            "accessory_pt": "electronic device skin",
            "primary_definition": "Dedicated gaming systems designed primarily for playing video games",
            "accessory_definition": "Decorative and protective vinyl covers or decals designed to customize the appearance of electronic devices",
            "primary_includes": [
                "Home game consoles (PlayStation, Xbox, Nintendo Switch, etc.)",
                "Handheld gaming consoles (Nintendo 3DS, Steam Deck, etc.)",
                "Retro gaming consoles",
                "Portable gaming devices"
            ],
            "primary_excludes": [
                "Gaming PCs/computers",
                "Gaming laptops",
                "Smartphones with gaming capabilities",
                "Game controllers (as separate accessories)",
                "Game software/cartridges"
            ],
            "accessory_includes": [
                "Console body skins/decals",
                "Controller skins",
                "Vinyl wraps for gaming devices",
                "Decorative stickers for consoles",
                "Protective skins with designs"
            ],
            "accessory_excludes": [
                "Hard protective cases",
                "Screen protectors",
                "Generic stickers not designed for gaming devices",
                "Phone skins",
                "Laptop skins",
                "Permanent modifications or paints"
            ],
            "primary_compatibility_criterion": "Video game consoles that can be customized and protected with precisely fitted device skins without blocking vents, ports, or functionality",
            "accessory_compatibility_criterion": "Device skins that are precisely cut and designed to fit the specific dimensions, button placements, and port locations of particular gaming console models"
        },
        "TABLET_COMPUTER-FLASH_MEMORY": {
            "primary_pt": "tablet computer",
            "accessory_pt": "flash memory card",
            "primary_definition": "Touchscreen portable computing devices primarily operated through touch",
            "accessory_definition": "Memory storage devices that can be inserted into electronic devices",
            "primary_includes": [
                "iPads and other brand tablets",
                "Android tablets",
                "Windows tablets",
                "Drawing tablets with storage capability",
                "2-in-1 tablet devices"
            ],
            "primary_excludes": [
                "Smartphones",
                "Laptops",
                "E-book readers (pure reading function)",
                "Tablet accessories (cases, stands, styluses, etc.)",
                "Digital photo frames"
            ],
            "accessory_includes": [
                "MicroSD cards",
                "SD cards",
                "SDHC cards",
                "SDXC cards",
                "USB-C flash drives (for tablets with USB-C)"
            ],
            "accessory_excludes": [
                "Internal tablet storage",
                "External hard drives",
                "Cloud storage services",
                "USB-A flash drives (incompatible with most tablets)",
                "Card readers"
            ],
            "primary_compatibility_criterion": "Tablet computers that have expandable storage capabilities and can accept flash memory cards or compatible storage devices for additional storage",
            "accessory_compatibility_criterion": "Flash memory cards or storage devices that can be inserted into or connected to tablet computers for expanding storage capacity"
        },
        "VIDEO_PROJECTOR-FLASH_MEMORY": {
            "primary_pt": "video projector",
            "accessory_pt": "flash memory card",
            "primary_definition": "Display devices that project images or videos onto screens or surfaces for presentation purposes",
            "accessory_definition": "Memory storage devices that can be inserted into electronic devices",
            "primary_includes": [
                "Business/office projectors",
                "Home theater projectors",
                "Portable/mini projectors",
                "Smart projectors with media playback",
                "Interactive projectors"
            ],
            "primary_excludes": [
                "Televisions",
                "Computer monitors",
                "Smartphones with projection features",
                "Projector accessories (screens, mounts, cables, etc.)",
                "Overhead projectors (non-digital)"
            ],
            "accessory_includes": [
                "SD cards",
                "SDHC cards",
                "SDXC cards",
                "MicroSD cards (with adapters)",
                "USB flash drives"
            ],
            "accessory_excludes": [
                "Internal projector storage",
                "External hard drives",
                "Proprietary memory cards not compatible with projectors",
                "Network storage devices",
                "Optical discs (DVDs, Blu-rays)"
            ],
            "primary_compatibility_criterion": "Video projectors that have built-in media players and can directly read and display content from flash memory cards or USB storage devices",
            "accessory_compatibility_criterion": "Flash memory cards or USB storage devices that can be inserted into video projectors for direct media playback and content storage"
        },
        "AIR_CONDITIONER-HVAC_AIR_FILTER": {
            "primary_pt": "air conditioner",
            "accessory_pt": "hvac air filter",
            "primary_definition": "Climate control devices that cool, heat, or condition indoor air",
            "accessory_definition": "Filtration components designed to remove particles and contaminants from air in HVAC systems",
            "primary_includes": [
                "Window air conditioners",
                "Split system air conditioners",
                "Central air conditioning units",
                "Portable air conditioners",
                "Mini-split air conditioners"
            ],
            "primary_excludes": [
                "Fans",
                "Air purifiers",
                "Humidifiers",
                "Dehumidifiers",
                "Heating units only"
            ],
            "accessory_includes": [
                "Pleated air filters",
                "HEPA filters for HVAC",
                "Activated carbon air filters",
                "Electrostatic air filters",
                "Washable air filters"
            ],
            "accessory_excludes": [
                "Standalone air purifier filters",
                "Vacuum cleaner filters",
                "Car air filters",
                "Water filters",
                "Oil filters"
            ],
            "primary_compatibility_criterion": "Air conditioners that have built-in air filtration systems and can accommodate replaceable air filters",
            "accessory_compatibility_criterion": "HVAC air filters that are sized and designed to fit specific air conditioner models and filter housings"
        },
        "CAMCORDER-CAMERA_ENCLOSURE": {
            "primary_pt": "camcorder",
            "accessory_pt": "camera enclosure",
            "primary_definition": "Portable video recording devices designed for capturing moving images and audio",
            "accessory_definition": "Protective housings or cases designed to protect cameras from environmental conditions",
            "primary_includes": [
                "Digital camcorders",
                "Professional video cameras",
                "Action camcorders",
                "Handheld camcorders",
                "Shoulder-mount camcorders"
            ],
            "primary_excludes": [
                "Still cameras (DSLRs, mirrorless)",
                "Smartphones",
                "Security cameras",
                "Webcams",
                "Live streaming cameras"
            ],
            "accessory_includes": [
                "Underwater housings for camcorders",
                "Weather-resistant cases",
                "Protective carrying cases",
                "Shock-resistant enclosures",
                "Dust-proof housings"
            ],
            "accessory_excludes": [
                "DSLR camera cases",
                "Smartphone cases",
                "Generic equipment cases",
                "Tripod cases",
                "Lens cases"
            ],
            "primary_compatibility_criterion": "Camcorders that can be protected and enclosed in specially designed housings for various environmental conditions",
            "accessory_compatibility_criterion": "Camera enclosures that are specifically designed to fit particular camcorder models with proper access to controls and lens"
        },
        "CAMCORDER-CAMERA_LENS_FILTERS": {
            "primary_pt": "camcorder",
            "accessory_pt": "camera lens filters",
            "primary_definition": "Portable video recording devices designed for capturing moving images and audio",
            "accessory_definition": "Optical filters that attach to camera lenses to modify light or add special effects",
            "primary_includes": [
                "Digital camcorders with filter threads",
                "Professional video cameras",
                "Camcorders with interchangeable lenses",
                "High-end consumer camcorders"
            ],
            "primary_excludes": [
                "Action cameras without filter threads",
                "Basic handheld camcorders without filter capability",
                "Smartphones",
                "Fixed-lens cameras without filter threads"
            ],
            "accessory_includes": [
                "UV filters",
                "Polarizing filters",
                "Neutral density (ND) filters",
                "Color correction filters",
                "Special effects filters",
                "Protection filters"
            ],
            "accessory_excludes": [
                "Lens caps",
                "Lens hoods",
                "Flash diffusers",
                "Smartphone lens attachments",
                "Projector filters"
            ],
            "primary_compatibility_criterion": "Camcorders that have lens filter threads or filter mounting systems compatible with standard camera filters",
            "accessory_compatibility_criterion": "Camera lens filters that match the filter thread size and mounting system of specific camcorder lenses"
        },
        "CAMCORDER-CAMERA_SUPPORT": {
            "primary_pt": "camcorder",
            "accessory_pt": "camera support",
            "primary_definition": "Portable video recording devices designed for capturing moving images and audio",
            "accessory_definition": "Support equipment designed to stabilize and position cameras during recording",
            "primary_includes": [
                "Digital camcorders",
                "Professional video cameras",
                "Action camcorders",
                "Handheld camcorders",
                "Shoulder-mount camcorders"
            ],
            "primary_excludes": [
                "Still cameras only",
                "Smartphones",
                "Security cameras (fixed installation)",
                "Webcams",
                "Microphones"
            ],
            "accessory_includes": [
                "Camera tripods",
                "Monopods",
                "Stabilizers and gimbals",
                "Shoulder rigs",
                "Camera sliders",
                "Video heads for tripods"
            ],
            "accessory_excludes": [
                "Microphone stands",
                "Lighting stands",
                "Monitor mounts",
                "Boom poles",
                "Desktop computer stands"
            ],
            "primary_compatibility_criterion": "Camcorders that have standard mounting threads (1/4-20 or 3/8-16) and can be mounted on camera support equipment",
            "accessory_compatibility_criterion": "Camera support equipment that can securely hold and stabilize camcorders with standard mounting systems"
        },
        "CAMCORDER-FLASH_MEMORY": {
            "primary_pt": "camcorder",
            "accessory_pt": "flash memory card",
            "primary_definition": "Portable video recording devices designed for capturing moving images and audio",
            "accessory_definition": "Memory storage devices that can be inserted into electronic devices",
            "primary_includes": [
                "Digital camcorders",
                "Professional video cameras with memory card slots",
                "Action camcorders",
                "Handheld camcorders",
                "4K/HD camcorders"
            ],
            "primary_excludes": [
                "Film-based cameras",
                "Tape-based camcorders only",
                "Smartphones",
                "Live streaming devices without storage",
                "Security cameras with built-in storage only"
            ],
            "accessory_includes": [
                "SD cards",
                "SDHC cards",
                "SDXC cards",
                "MicroSD cards",
                "CF cards (CompactFlash)",
                "High-speed video recording cards"
            ],
            "accessory_excludes": [
                "Internal camcorder storage",
                "External hard drives",
                "Tape cartridges",
                "USB drives for computers only",
                "Network storage devices"
            ],
            "primary_compatibility_criterion": "Camcorders that have memory card slots and can record video content directly to flash memory cards",
            "accessory_compatibility_criterion": "Flash memory cards that are compatible with camcorder memory card slots and meet video recording speed requirements"
        },
        "CAMERA_DIGITAL-CAMERA_ENCLOSURE": {
            "primary_pt": "normal camera",
            "accessory_pt": "camera enclosure",
            "primary_definition": "Digital camera devices for capturing still photos and videos",
            "accessory_definition": "Protective housings or cases designed to protect cameras from environmental conditions",
            "primary_includes": [
                "Digital SLR cameras (DSLR)",
                "Mirrorless cameras",
                "Point-and-shoot digital cameras",
                "Action cameras",
                "Professional cameras"
            ],
            "primary_excludes": [
                "Smartphones",
                "Tablets",
                "Camcorders",
                "Security cameras",
                "Webcams"
            ],
            "accessory_includes": [
                "Underwater housings for cameras",
                "Weather-resistant cases",
                "Dust-proof enclosures",
                "Shock-resistant housings",
                "Cold weather protections"
            ],
            "accessory_excludes": [
                "Camera bags and cases (soft)",
                "Smartphone cases",
                "Lens cases",
                "Tripod cases",
                "Generic protective cases"
            ],
            "primary_compatibility_criterion": "Digital cameras that can be protected and enclosed in specially designed housings for various environmental conditions",
            "accessory_compatibility_criterion": "Camera enclosures that are specifically designed to fit particular camera models with proper access to controls and lens"
        },
        "CAMERA_DIGITAL-CAMERA_FLASH": {
            "primary_pt": "normal camera",
            "accessory_pt": "camera flash",
            "primary_definition": "Digital camera devices for capturing still photos and videos",
            "accessory_definition": "External lighting devices designed to provide additional illumination for photography",
            "primary_includes": [
                "Digital SLR cameras (DSLR)",
                "Mirrorless cameras",
                "Professional cameras with hot shoe",
                "Cameras with flash sync capabilities"
            ],
            "primary_excludes": [
                "Point-and-shoot cameras with built-in flash only",
                "Smartphones",
                "Action cameras without flash capability",
                "Film cameras",
                "Security cameras"
            ],
            "accessory_includes": [
                "External speedlight/strobe flashes",
                "Hot shoe mounted flashes",
                "Ring flashes",
                "Studio strobes with sync",
                "Wireless flash units"
            ],
            "accessory_excludes": [
                "Built-in camera flashes",
                "Continuous lighting",
                "Smartphone flash",
                "Flashlights",
                "Video lights"
            ],
            "primary_compatibility_criterion": "Digital cameras that have hot shoe mounts or flash sync capabilities and can trigger external flash units",
            "accessory_compatibility_criterion": "Camera flashes that are compatible with the camera's hot shoe mount or flash sync system"
        },
        "CAMERA_DIGITAL-CAMERA_LENS_FILTERS": {
            "primary_pt": "normal camera",
            "accessory_pt": "camera lens filters",
            "primary_definition": "Digital camera devices for capturing still photos and videos",
            "accessory_definition": "Optical filters that attach to camera lenses to modify light or add special effects",
            "primary_includes": [
                "Digital SLR cameras (DSLR)",
                "Mirrorless cameras",
                "Cameras with filter-threaded lenses",
                "Professional cameras"
            ],
            "primary_excludes": [
                "Point-and-shoot cameras without filter threads",
                "Smartphones",
                "Action cameras without filter capability",
                "Fixed lens cameras without filter threads"
            ],
            "accessory_includes": [
                "UV filters",
                "Polarizing filters (CPL)",
                "Neutral density (ND) filters",
                "Graduated filters",
                "Color filters",
                "Close-up filters"
            ],
            "accessory_excludes": [
                "Lens caps",
                "Lens hoods",
                "Smartphone lens attachments",
                "Projector filters",
                "Lighting filters"
            ],
            "primary_compatibility_criterion": "Digital cameras with lenses that have filter threads and can accept screw-on or clip-on lens filters",
            "accessory_compatibility_criterion": "Camera lens filters that match the filter thread diameter and mounting system of specific camera lenses"
        },
        "CAMERA_FILM-PHOTOGRAPHIC_FILM": {
            "primary_pt": "film camera or instant camera",
            "accessory_pt": "photographic film",
            "primary_definition": "Analog cameras that use photographic film to capture and store images",
            "accessory_definition": "Light-sensitive material used in analog photography to record images",
            "primary_includes": [
                "35mm film cameras",
                "Medium format film cameras",
                "Large format film cameras",
                "Instant cameras (Polaroid-type)",
                "Disposable film cameras"
            ],
            "primary_excludes": [
                "Digital cameras",
                "Smartphones",
                "Video cameras",
                "Security cameras",
                "Webcams"
            ],
            "accessory_includes": [
                "35mm color film",
                "35mm black and white film",
                "120 medium format film",
                "Instant film cartridges",
                "Large format sheet film",
                "APS film"
            ],
            "accessory_excludes": [
                "Digital memory cards",
                "Film processing chemicals",
                "Photo paper",
                "Film canisters (empty)",
                "X-ray film"
            ],
            "primary_compatibility_criterion": "Film cameras or instant cameras that can load and use specific types and formats of photographic film",
            "accessory_compatibility_criterion": "Photographic film that matches the format and specifications required by specific film camera types"
        },
        "DIGITAL_VIDEO_RECORDER-COMPUTER_DRIVE_OR_STORAGE": {
            "primary_pt": "digital video recorder",
            "accessory_pt": "computer drive or storage",
            "primary_definition": "Electronic devices that record and store video content digitally",
            "accessory_definition": "Storage devices designed for installation in computers and compatible electronic devices",
            "primary_includes": [
                "DVR systems",
                "Network video recorders (NVRs)",
                "Set-top box DVRs",
                "Standalone digital recorders",
                "CCTV recording systems"
            ],
            "primary_excludes": [
                "Analog VCR recorders",
                "Personal computers",
                "Smartphones",
                "Camcorders",
                "Live streaming devices"
            ],
            "accessory_includes": [
                "Internal SATA hard drives",
                "Surveillance-grade hard drives",
                "High-capacity storage drives",
                "SSD drives for DVR systems",
                "RAID-compatible drives"
            ],
            "accessory_excludes": [
                "External USB drives",
                "Flash memory cards",
                "Optical drives",
                "Network attached storage (NAS) units",
                "Cloud storage services"
            ],
            "primary_compatibility_criterion": "Digital video recorders that have internal drive bays and can accommodate additional or replacement storage drives",
            "accessory_compatibility_criterion": "Computer drives and storage devices that are compatible with DVR systems and meet video recording requirements"
        },
        "GPS_OR_NAVIGATION_SYSTEM-FLASH_MEMORY": {
            "primary_pt": "GPS or navigation system",
            "accessory_pt": "flash memory card",
            "primary_definition": "Electronic devices that provide location tracking and navigation guidance",
            "accessory_definition": "Memory storage devices that can be inserted into electronic devices",
            "primary_includes": [
                "Dedicated GPS navigation units",
                "Vehicle-mounted navigation systems",
                "Handheld GPS devices",
                "Marine GPS systems",
                "Aviation GPS units"
            ],
            "primary_excludes": [
                "Smartphones with GPS (primarily communication devices)",
                "Tablets with GPS",
                "Smartwatches",
                "GPS tracking devices without navigation display",
                "Survey equipment"
            ],
            "accessory_includes": [
                "SD cards",
                "SDHC cards",
                "SDXC cards",
                "MicroSD cards",
                "GPS-specific memory cards"
            ],
            "accessory_excludes": [
                "Internal GPS storage",
                "External hard drives",
                "USB drives for computers",
                "Cloud storage services",
                "SIM cards"
            ],
            "primary_compatibility_criterion": "GPS or navigation systems that have memory card slots and can store map data, routes, and waypoints on flash memory",
            "accessory_compatibility_criterion": "Flash memory cards that are compatible with GPS navigation systems and can store mapping and navigation data"
        },
        "NOTEBOOK_COMPUTER-FLASH_MEMORY": {
            "primary_pt": "notebook computer",
            "accessory_pt": "flash memory card",
            "primary_definition": "Portable personal computers with integrated screen, keyboard, and trackpad",
            "accessory_definition": "Memory storage devices that can be inserted into electronic devices",
            "primary_includes": [
                "Traditional laptops",
                "Gaming laptops",
                "Business laptops",
                "Ultrabooks",
                "2-in-1 laptops with card readers"
            ],
            "primary_excludes": [
                "Pure tablets",
                "Desktop computers",
                "Servers",
                "Smartphones",
                "Laptops without card readers"
            ],
            "accessory_includes": [
                "SD cards",
                "SDHC cards",
                "SDXC cards",
                "MicroSD cards",
                "CompactFlash cards",
                "USB flash drives"
            ],
            "accessory_excludes": [
                "Internal laptop storage",
                "External hard drives",
                "Network storage",
                "Cloud storage services",
                "SIM cards"
            ],
            "primary_compatibility_criterion": "Notebook computers that have memory card readers and can access data from various types of flash memory cards",
            "accessory_compatibility_criterion": "Flash memory cards that are compatible with notebook computer card readers and file systems"
        },
        "PERSONAL_COMPUTER-FLASH_MEMORY": {
            "primary_pt": "personal computer",
            "accessory_pt": "flash memory card",
            "primary_definition": "Desktop personal computer systems",
            "accessory_definition": "Memory storage devices that can be inserted into electronic devices",
            "primary_includes": [
                "Desktop computers with card readers",
                "All-in-one computers",
                "Gaming desktops",
                "Workstations with card slots",
                "Mini PCs with card readers"
            ],
            "primary_excludes": [
                "Laptops",
                "Tablets",
                "Servers",
                "Smartphones",
                "Computers without card readers"
            ],
            "accessory_includes": [
                "SD cards",
                "SDHC cards",
                "SDXC cards",
                "MicroSD cards",
                "CompactFlash cards",
                "USB flash drives"
            ],
            "accessory_excludes": [
                "Internal computer storage",
                "External hard drives",
                "Network storage",
                "Cloud storage services",
                "SIM cards"
            ],
            "primary_compatibility_criterion": "Personal computers that have memory card readers and can access data from various types of flash memory cards",
            "accessory_compatibility_criterion": "Flash memory cards that are compatible with personal computer card readers and file systems"
        },
        "PERSONAL_COMPUTER-INPUT_MOUSE": {
            "primary_pt": "personal computer",
            "accessory_pt": "input mouse",
            "primary_definition": "Desktop personal computer systems",
            "accessory_definition": "Pointing devices used to control cursor movement and interact with computer interfaces",
            "primary_includes": [
                "Desktop computers",
                "All-in-one computers",
                "Gaming desktops",
                "Workstations",
                "Mini PCs"
            ],
            "primary_excludes": [
                "Laptops (have built-in trackpads)",
                "Tablets",
                "Smartphones",
                "Servers without user interfaces",
                "Gaming consoles"
            ],
            "accessory_includes": [
                "Wired USB mice",
                "Wireless mice",
                "Gaming mice",
                "Optical mice",
                "Laser mice",
                "Ergonomic mice"
            ],
            "accessory_excludes": [
                "Trackpads",
                "Touchscreens",
                "Graphics tablets",
                "Game controllers",
                "TV remotes"
            ],
            "primary_compatibility_criterion": "Personal computers that have USB ports or wireless connectivity and can support external mouse input devices",
            "accessory_compatibility_criterion": "Input mice that are compatible with personal computer operating systems and connection methods (USB, wireless, etc.)"
        },
        "PRINTER-LASER_PRINTER_TONER": {
            "primary_pt": "printer",
            "accessory_pt": "laser printer toner",
            "primary_definition": "Printing devices that produce hard copies of digital documents or images",
            "accessory_definition": "Powder-based printing consumables specifically designed for laser printing technology",
            "primary_includes": [
                "Laser printers",
                "All-in-one laser printers",
                "Monochrome laser printers",
                "Color laser printers",
                "Multifunction laser devices"
            ],
            "primary_excludes": [
                "Inkjet printers",
                "Dot matrix printers",
                "3D printers",
                "Thermal printers",
                "Photo printers (inkjet-based)"
            ],
            "accessory_includes": [
                "Original manufacturer toner cartridges",
                "Compatible/third-party toner cartridges",
                "Black toner cartridges",
                "Color toner cartridges (cyan, magenta, yellow)",
                "High-yield toner cartridges"
            ],
            "accessory_excludes": [
                "Inkjet printer ink cartridges",
                "Printer paper",
                "Ribbon cartridges",
                "3D printer filaments",
                "Thermal paper"
            ],
            "primary_compatibility_criterion": "Laser printers that use toner-based printing technology and can accept specific toner cartridges",
            "accessory_compatibility_criterion": "Laser printer toner cartridges that are specifically designed and manufactured to work with particular laser printer models"
        },
        "SECURITY_CAMERA-FLASH_MEMORY": {
            "primary_pt": "security camera",
            "accessory_pt": "flash memory card",
            "primary_definition": "Camera devices designed for security monitoring and surveillance",
            "accessory_definition": "Memory storage devices that can be inserted into electronic devices",
            "primary_includes": [
                "IP security cameras with local storage",
                "Wireless security cameras",
                "Battery-powered security cameras",
                "Smart doorbell cameras",
                "Trail/wildlife cameras"
            ],
            "primary_excludes": [
                "Digital cameras for photography",
                "Camcorders",
                "Webcams for video calls",
                "Security cameras with cloud-only storage",
                "Analog CCTV cameras"
            ],
            "accessory_includes": [
                "MicroSD cards",
                "SD cards",
                "SDHC cards",
                "SDXC cards",
                "High-endurance memory cards"
            ],
            "accessory_excludes": [
                "Internal camera storage",
                "External hard drives",
                "Network storage devices",
                "Cloud storage services",
                "USB drives for computers"
            ],
            "primary_compatibility_criterion": "Security cameras that have memory card slots and can record surveillance footage directly to flash memory cards",
            "accessory_compatibility_criterion": "Flash memory cards that are compatible with security camera storage systems and can handle continuous recording requirements"
        },
        "VENT_HOOD-HVAC_AIR_FILTER": {
            "primary_pt": "vent hood",
            "accessory_pt": "hvac air filter",
            "primary_definition": "Kitchen appliances designed to remove smoke, odors, and airborne particles during cooking",
            "accessory_definition": "Filtration components designed to remove particles and contaminants from air in HVAC systems",
            "primary_includes": [
                "Range hoods",
                "Island vent hoods",
                "Under-cabinet vent hoods",
                "Wall-mounted vent hoods",
                "Downdraft ventilation systems"
            ],
            "primary_excludes": [
                "Air conditioners",
                "Air purifiers",
                "Fans",
                "Microwave vent fans",
                "Bathroom exhaust fans"
            ],
            "accessory_includes": [
                "Grease filters for range hoods",
                "Charcoal filters",
                "Aluminum mesh filters",
                "Baffle filters",
                "Ducted hood filters"
            ],
            "accessory_excludes": [
                "HVAC system filters",
                "Air purifier filters",
                "Vacuum cleaner filters",
                "Car air filters",
                "Water filters"
            ],
            "primary_compatibility_criterion": "Vent hoods that have replaceable filter systems and can accommodate specific types of air filters",
            "accessory_compatibility_criterion": "HVAC air filters that are sized and designed to fit specific vent hood models and filter housings"
        },
        "VIDEO_GAME_CONSOLE-HEADPHONES": {
            "primary_pt": "video game console",
            "accessory_pt": "headphones or earphones",
            "primary_definition": "Dedicated gaming systems designed primarily for playing video games",
            "accessory_definition": "Audio devices worn on or in the ears for personal listening",
            "primary_includes": [
                "Home game consoles (PlayStation, Xbox, Nintendo Switch, etc.)",
                "Handheld gaming consoles",
                "Retro gaming consoles",
                "VR gaming systems"
            ],
            "primary_excludes": [
                "Gaming PCs",
                "Gaming laptops",
                "Smartphones with gaming capabilities",
                "Tablets with games",
                "Game controllers (as separate accessories)"
            ],
            "accessory_includes": [
                "Gaming headsets with microphones",
                "Wireless gaming headphones",
                "Console-specific headphones",
                "3.5mm wired headphones",
                "USB gaming headsets",
                "Bluetooth gaming headphones"
            ],
            "accessory_excludes": [
                "Speakers (external audio devices)",
                "Computer-specific headsets",
                "Professional audio equipment",
                "Hearing aids",
                "Phone-specific earbuds"
            ],
            "primary_compatibility_criterion": "Video game consoles that have audio output capabilities and can connect to headphones for gaming audio and communication",
            "accessory_compatibility_criterion": "Headphones or earphones that are compatible with video game console audio systems and connection methods"
        }
    }

    return comp_criterion[pt_pair]



def main_prompt(item_name, bullet_points, acc_pri, compatibility_criterion, base64_image, reason=False) -> str:
    # Format product compatibility information
    primary_info = f"""
Product: {compatibility_criterion['primary_pt']}
Definition: {compatibility_criterion['primary_definition']}

Product Includes:
{chr(10).join([f"• {item}" for item in compatibility_criterion['primary_includes']])}

Product Excludes:
{chr(10).join([f"• {item}" for item in compatibility_criterion['primary_excludes']])}
"""

    accessory_info = f"""
Product: {compatibility_criterion['accessory_pt']}
Definition: {compatibility_criterion['accessory_definition']}

Product Includes:
{chr(10).join([f"• {item}" for item in compatibility_criterion['accessory_includes']])}

Product Excludes:
{chr(10).join([f"• {item}" for item in compatibility_criterion['accessory_excludes']])}
"""

    # Select the appropriate compatibility criterion based on acc_pri
    if acc_pri == "primary":
        selected_compatibility_criterion = compatibility_criterion['primary_compatibility_criterion']
    else:  # acc_pri == "accessory"
        selected_compatibility_criterion = compatibility_criterion['accessory_compatibility_criterion']

    if base64_image and reason:
        prompt = f"""
You are a professional product compatibility verification expert. Your task is to determine whether a given product from one specified category is within a reasonable and typical compatibility scope with another product category, based on provided product details, image, and explicit compatibility criteria.

## Compatibility Standards

Given Product:
{primary_info if acc_pri == 'primary' else accessory_info}

Target Product Category: {compatibility_criterion['accessory_pt'] if acc_pri == "primary" else compatibility_criterion['primary_pt']}

Compatibility Criterion: {selected_compatibility_criterion}

## Evaluation Steps

Please follow these steps for analysis:

1. **Product Identification**: Carefully analyze the given product title, description, and image to identify the product's core functions and features
2. **Category Matching**: Check if the given product is a valid product based on the definition and includes/excludes lists
3. **Compatibility Assessment**: Evaluate if the given product is a valid product and falls within the typical compatibility scope based on the target product category and compatibility criterion
4. **Edge Case Handling**: For ambiguous cases, prioritize the given product's primary function and typical use scenarios

## Input Information

Product Title: {item_name}

Product Description:
{bullet_points}

## Output Requirements

Please output strictly in the following JSON format, without any other content:

{{
    "in_scope": "yes/no",
    "reason": "Very simple explanation of the judgment reasoning"
}}

## Judgment Principles

- If the given product clearly belongs to the given product category and falls within the compatibility scope, judge as "yes"
- If the given product clearly belongs to excluded types or falls outside the compatibility scope, judge as "no"
- For edge cases, base judgment on the given product's primary function and typical compatibility scenarios
- The reasoning should be simple and specifically reference given product features and compatibility standards

Now please make your judgment based on the product image and the above information:
"""
    elif not base64_image and reason:
        prompt = f"""
You are a professional product compatibility verification expert. Your task is to determine whether a given product from one specified category is within a reasonable and typical compatibility scope with another product category, based on provided product details and explicit compatibility criteria.

## Compatibility Standards

Given Product:
{primary_info if acc_pri == 'primary' else accessory_info}

Target Product Category: {compatibility_criterion['accessory_pt'] if acc_pri == "primary" else compatibility_criterion['primary_pt']}

Compatibility Criterion: {selected_compatibility_criterion}

## Evaluation Steps

Please follow these steps for analysis:

1. **Product Identification**: Carefully analyze the given product title, description, and image to identify the product's core functions and features
2. **Category Matching**: Check if the given product is a valid product based on the definition and includes/excludes lists
3. **Compatibility Assessment**: Evaluate if the given product is a valid product and falls within the typical compatibility scope based on the target product category and compatibility criterion
4. **Edge Case Handling**: For ambiguous cases, prioritize the given product's primary function and typical use scenarios

## Input Information

Product Title: {item_name}

Product Description:
{bullet_points}

## Output Requirements

Please output strictly in the following JSON format, without any other content:

{{
    "in_scope": "yes/no",
    "reason": "Very simple explanation of the judgment reasoning"
}}

## Judgment Principles

- If the given product clearly belongs to the given product category and falls within the compatibility scope, judge as "yes"
- If the given product clearly belongs to excluded types or falls outside the compatibility scope, judge as "no"
- For edge cases, base judgment on the given product's primary function and typical compatibility scenarios
- The reasoning should be simple and specifically reference given product features and compatibility standards

Now please make your judgment based on the above information:
"""
    elif base64_image and not reason:
        prompt = f"""
You are a professional product compatibility verification expert. Your task is to determine whether a given product from one specified category is within a reasonable and typical compatibility scope with another product category, based on provided product details, image, and explicit compatibility criteria.

## Compatibility Standards

Given Product:
{primary_info if acc_pri == 'primary' else accessory_info}

Target Product Category: {compatibility_criterion['accessory_pt'] if acc_pri == "primary" else compatibility_criterion['primary_pt']}

Compatibility Criterion: {selected_compatibility_criterion}

## Evaluation Steps

Please follow these steps for analysis:

1. **Product Identification**: Carefully analyze the given product title, description, and image to identify the product's core functions and features
2. **Category Matching**: Check if the given product is a valid product based on the definition and includes/excludes lists
3. **Compatibility Assessment**: Evaluate if the given product is a valid product and falls within the typical compatibility scope based on the target product category and compatibility criterion
4. **Edge Case Handling**: For ambiguous cases, prioritize the given product's primary function and typical use scenarios

## Input Information

Product Title: {item_name}

Product Description:
{bullet_points}

## Output Requirements

Please output strictly in the following JSON format, without any other content:

{{
    "in_scope": "yes/no"
}}

## Judgment Principles

- If the given product clearly belongs to the given product category and falls within the compatibility scope, judge as "yes"
- If the given product clearly belongs to excluded types or falls outside the compatibility scope, judge as "no"
- For edge cases, base judgment on the given product's primary function and typical compatibility scenarios

Now please make your judgment based on the product image and the above information:
"""
    elif not base64_image and not reason:
        prompt = f"""
You are a professional product compatibility verification expert. Your task is to determine whether a given product from one specified category is within a reasonable and typical compatibility scope with another product category, based on provided product details and explicit compatibility criteria.

## Compatibility Standards

Given Product:
{primary_info if acc_pri == 'primary' else accessory_info}

Target Product Category: {compatibility_criterion['accessory_pt'] if acc_pri == "primary" else compatibility_criterion['primary_pt']}

Compatibility Criterion: {selected_compatibility_criterion}

## Evaluation Steps

Please follow these steps for analysis:

1. **Product Identification**: Carefully analyze the given product title, description, and image to identify the product's core functions and features
2. **Category Matching**: Check if the given product is a valid product based on the definition and includes/excludes lists
3. **Compatibility Assessment**: Evaluate if the given product is a valid product and falls within the typical compatibility scope based on the target product category and compatibility criterion
4. **Edge Case Handling**: For ambiguous cases, prioritize the given product's primary function and typical use scenarios

## Input Information

Product Title: {item_name}

Product Description:
{bullet_points}

## Output Requirements

Please output strictly in the following JSON format, without any other content:

{{
    "in_scope": "yes/no"
}}

## Judgment Principles

- If the given product clearly belongs to the given product category and falls within the compatibility scope, judge as "yes"
- If the given product clearly belongs to excluded types or falls outside the compatibility scope, judge as "no"
- For edge cases, base judgment on the given product's primary function and typical compatibility scenarios

Now please make your judgment based on the above information:
"""
    return prompt
