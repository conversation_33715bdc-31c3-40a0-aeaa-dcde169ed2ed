def create_extraction_prompt(product_text: str) -> str:
    """
    Create prompt template for extracting model information
    """
    prompt = f"""
You are a professional text analysis expert. Your task is to extract complete original sentences from product descriptions that mention vacuum cleaner models, vacuum cleaner names, or vacuum cleaner compatibility information.

Please carefully analyze the following product description text and identify all complete sentences that mention vacuum cleaner models, vacuum cleaner names, compatible vacuum cleaner, or related information.

Product Description Text:
{product_text}

Extraction Requirements:
1. Find all sentences mentioning specific vacuum cleaner models (e.g., Honeywell 50250 50250-S OEM, etc.)
2. Find all sentences mentioning vacuum cleaner series (e.g., Dreame X40 series, etc.)
3. Find all sentences mentioning vacuum cleaner compatibility (e.g., Compatible with, Works with, Suitable for, etc.)
4. Extract complete original sentences without modification or summarization
5. If a sentence is long but contains vacuum cleaner model information, extract the complete sentence
6. If no vacuum cleaner model related information is found, return "No relevant information"

Please return the result in the following JSON format only, without any other content:

{{
    "extracted_sentences": [
        "Original sentence 1",
        "Original sentence 2",
        "Original sentence 3"
    ]
}}

Important Notes:
- Return only JSON format results
- If no relevant information is found, return {{"extracted_sentences": ["No relevant information"]}}
- Ensure extracted sentences are complete original sentences from the text
- Do not modify, paraphrase, or summarize the sentences
- Extract the exact text as it appears in the original description
"""
    return prompt


def extract_models_from_response(response: str) -> str:
    """
    Extract model related sentences from LLM response
    """
    try:
        import json
        import re
        
        # Try to extract JSON part from response
        json_match = re.search(r'\{.*?\}', response, re.DOTALL)
        if json_match:
            json_str = json_match.group()
            result = json.loads(json_str)
            
            if 'extracted_sentences' in result:
                sentences = result['extracted_sentences']
                if sentences and sentences != ["No relevant information"] and sentences != ["无相关信息"]:
                    return " | ".join(sentences)
                else:
                    return "No relevant information"
            else:
                return "Parsing failed"
        else:
            return "Parsing failed"
    except Exception as e:
        return f"Parsing error: {str(e)}"