import os
import re
import sys
import boto3
import json
import concurrent.futures
from tqdm import tqdm
import pandas as pd
import time
from collections import defaultdict

BASE_DIR = os.path.dirname(os.path.abspath('{}/../'.format(__file__)))
sys.path.append(BASE_DIR)

from src.utils import get_main_image
from src.build_prompt import main_prompt, get_comp_criterion
from src.constant import model_name_id_map


class LLM:
    def __init__(self, config=None, existing_claude_response=None, model_name="Claude 3.5 Sonnet"):
        self.existing_claude_response = existing_claude_response
        self.model_name = model_name
        self.all_clients = self._get_bedrock_clients()
        self.clients = {index: (model_id, client) for model_id in model_name_id_map[model_name] for index, client in enumerate(self.all_clients[model_id])}

    def _get_bedrock_clients(self):
        clients = defaultdict(list)
        compatible_regions = defaultdict(list)

        # read claude account information from dynamodb
        session = boto3.Session(profile_name='compatibility', region_name="us-east-1")
        dynamodb = session.resource('dynamodb')
        table = dynamodb.Table('claude_account')
        response = table.scan()

        client_count = 0
        
        for item in tqdm(response['Items'], desc="Getting bedrock clients"):
            # 测试Claude模型
            claude_body = json.dumps({
                "max_tokens": 1024,
                "system": f"",
                "messages": [{"role": "user", "content": [
                            {
                                "type": "text",
                                "text": "Hello"
                            }
                        ]}],
                "anthropic_version": "bedrock-2023-05-31"
            })

            test_client = boto3.client(service_name='bedrock-runtime', aws_access_key_id=item['access_key_id'],region_name=item['region_name'], aws_secret_access_key=item['secret_access_key'])
            
            for model_id in tqdm(model_name_id_map[self.model_name], desc="Testing model compatibility"):
                    try:
                        test_client.invoke_model(body=claude_body, modelId=model_id)
                        compatible_regions[model_id].append([item['access_key_id'], item['secret_access_key'], item['region_name']])
                        client_count += 1
                        print(f"✅ 区域 {item['region_name']} 支持 {model_id} 模型")
                    except Exception as ex:
                        print(f"❌ {model_id}模型测试失败 ({item['region_name']}): {ex}")

        print(f"totally {client_count} clients are compatible with {self.model_name} model")
        
        for model_id, regions in compatible_regions.items():
            max_clients = len(regions) * 3
            for i in range(max_clients):
                try:
                    client = boto3.client(service_name='bedrock-runtime',
                                                        aws_access_key_id=compatible_regions[model_id][i % len(compatible_regions[model_id])][0],
                                                        region_name=compatible_regions[model_id][i % len(compatible_regions[model_id])][2],
                                                        aws_secret_access_key=compatible_regions[model_id][i % len(compatible_regions[model_id])][1])
                    clients[model_id].append(client)
                except Exception as ex:
                    print(f"❌ {model_id}模型测试失败 ({item['region_name']}): {ex}")

        return clients

    def _data_generator(self, data_list):
        for data in data_list:
            yield data

    def build_prompt(self, row):
        image_base64 = get_main_image(self.config['current_pt'], self.config['acc_pri'], self.config['mp'], row['asin'])
        acc_pri = self.config['acc_pri']
        compatibility_criterion = self.config['compatibility_criterion']

        item_name = row['item_name']
        if "bullet_point" in row.keys():
            bullet_points = row['bullet_point']
        else:
            bullet_point_list = []
            for i in range(1, 6):
                if pd.isna(row[f'bullet_point{i}']) or str(row[f'bullet_point{i}']).strip() == "":
                    continue
                bullet_point_list.append(row[f'bullet_point{i}'])
            bullet_points = "\n".join(bullet_point_list)

        prompt = main_prompt(item_name, bullet_points, acc_pri, compatibility_criterion, image_base64)

        return prompt, image_base64

    def get_claude_response(self, client_idx, prompt, image_base64):
        if image_base64 is None:
            body = json.dumps({
                "max_tokens": 1024,
                "system": "",
                "messages": [{"role": "user", "content": [
                    {
                        "type": "text",
                        "text": prompt
                    }
                ]}],
                "anthropic_version": "bedrock-2023-05-31"
            })
        else:
            body = json.dumps({
                "max_tokens": 1024,
                "system": "",
                "messages": [{"role": "user", "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image",
                            "source": {
                                "type": "base64",
                                "media_type": "image/jpeg",
                                "data": image_base64
                            }
                        }
                ]}],
                "anthropic_version": "bedrock-2023-05-31"
            })

        # modelId = "anthropic.claude-3-5-sonnet-20241022-v2:0"
        # modelId = "anthropic.claude-3-5-sonnet-20240620-v1:0"
        # modelId = "anthropic.claude-3-5-haiku-20241022-v1:0"
        modelId = self.clients[client_idx][0]
        client = self.clients[client_idx][1]

        retry = 0
        max_retry = 20
        while retry < max_retry:
            try:
                response = client.invoke_model(body=body, modelId=modelId)
                response_body = json.loads(response.get('body').read())
                answer = response_body.get('content')
                answer = answer[0]['text']
                break
            except Exception as e:
                if retry >= 3:
                    modelId = "anthropic.claude-3-sonnet-20240229-v1:0"
                # print("error: ", e)
                # modelId = 'anthropic.claude-3-5-sonnet-20241022-v2:0'
                # modelId = "anthropic.claude-3-5-haiku-20241022-v1:0"
                time.sleep(1)
                retry += 1
        else:
            answer = 'no output'

        if answer != 'no output':
            answer = re.sub(r"\n", "", answer)
        return answer

    def call_claude(self, client_idx, row):
        if row['asin'] in self.existing_claude_response:
            return {"asin": row['asin'], "claude_response": self.existing_claude_response[row['asin']]}
        
        prompt, image_base64 = self.build_prompt(row)

        response = self.get_claude_response(client_idx, prompt, image_base64)
        return {"asin": row['asin'], "claude_response": response}

    def multi_thrad_run(self, df, output_file):
        data_gen = self._data_generator(df.to_dict(orient='records'))
        pbar = tqdm(range(df.shape[0]))
        max_clients = min(len(self.clients), df.shape[0])

        with open(output_file, "a+") as f:
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_clients) as executor:
                futures = {executor.submit(self.call_claude, i, next(data_gen)): i for i in range(max_clients)}
                # result_list = []
                while futures:
                    done, _ = concurrent.futures.wait(futures, return_when=concurrent.futures.FIRST_COMPLETED)
                    for future in done:
                        client_id = futures.pop(future)
                        try:
                            result = future.result()
                            f.write(json.dumps(result) + '\n')

                            try:
                                next_data = next(data_gen)
                                futures[executor.submit(self.call_claude, client_id, next_data)] = client_id
                            except StopIteration:
                                pass

                        except Exception as e:
                            print(e)
                            try:
                                next_data = next(data_gen)
                                futures[executor.submit(self.call_claude, client_id, next_data)] = client_id
                            except StopIteration:
                                pass

                        pbar.update(1)
