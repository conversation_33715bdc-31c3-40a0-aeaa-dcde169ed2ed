import pandas as pd
import boto3
import base64
import os
import numpy as np

s3 = boto3.client('s3')


def read_input_data(df_file: str) -> pd.DataFrame:
    data_df = pd.DataFrame()
    if os.path.exists(df_file):
        data_df = pd.read_csv(df_file, sep='\t', encoding='utf8', dtype='str', escapechar='\\', on_bad_lines='skip', quoting=3)
    # else:
    #     s3_client = boto3.client('s3')
    #     response = s3_client.get_object(Bucket='ude-image-extraction', Key=df_file)
    #     data_df = pd.read_csv(response['Body'], sep='\t', encoding='utf8', dtype='str', escapechar='\\', on_bad_lines='skip', quoting=3)
    return data_df


def get_main_image(current_pt: str, acc_pri: str, mp: str, asin: str) -> str:
    # get the image from S3 and convert to base64
    bucket_name = 'compatibility-data-gen'
    key = f"in_scope/{current_pt}_{acc_pri}/images/{mp}/{asin}.jpg"
    try:
        response = s3.get_object(Bucket=bucket_name, Key=key)
        image = response['Body'].read()
        return base64.b64encode(image).decode('utf-8')
    except Exception as e:
        print(f"Error getting image for {asin}: {e}")
        return None


def layer_sample(df: pd.DataFrame) -> pd.DataFrame:
    np.random.seed(42)
    df_sorted = df.sort_values(by='gvc', ascending=False).reset_index(drop=True)

    bins = [0, 0.05, 0.15, 0.35, 0.65, 1.0]
    labels = ['Top5%', '5%-15%', '15%-35%', '35%-65%', '65%-100%']
    df_sorted['gvc_tier'] = pd.qcut(df_sorted.index / len(df_sorted), q=bins, labels=labels)

    # 定义每个层次的初始抽样比例
    initial_sampling_ratio = {
        'Top5%': 0.90,        # 第一层（销量最高）抽取90%
        '5%-15%': 0.6,       # 第二层抽取60%
        '15%-35%': 0.4,      # 第三层抽取40%
        '35%-65%': 0.2,      # 第四层抽取20%
        '65%-100%': 0.1     # 第五层抽取10%
    }
    
    max_samples = 10000
    
    # 计算每层的实际数量
    tier_counts = df_sorted['gvc_tier'].value_counts()
    print("各层样本数量：", tier_counts.to_dict())
    
    # 计算初始抽样数量
    initial_sample_counts = {}
    for tier, ratio in initial_sampling_ratio.items():
        if tier in tier_counts:
            initial_sample_counts[tier] = int(tier_counts[tier] * ratio)
        else:
            initial_sample_counts[tier] = 0
    
    total_initial_samples = sum(initial_sample_counts.values())
    print(f"初始预计抽样总数：{total_initial_samples}")
    
    # 如果初始抽样数量超过限制，需要调整
    if total_initial_samples > max_samples:
        # 计算缩放因子
        scale_factor = max_samples / total_initial_samples
        print(f"缩放因子：{scale_factor:.4f}")
        
        # 按比例缩放每层的样本数量
        adjusted_sample_counts = {}
        for tier, count in initial_sample_counts.items():
            adjusted_sample_counts[tier] = max(1, int(count * scale_factor))  # 至少保留1个样本
        
        # 检查缩放后的总数
        adjusted_total = sum(adjusted_sample_counts.values())
        
        # 如果缩放后仍然超过限制，进行微调
        if adjusted_total > max_samples:
            excess = adjusted_total - max_samples
            # 从低优先级层开始减少样本
            tiers_order = ['65%-100%', '35%-65%', '15%-35%', '5%-15%', 'Top5%']
            for tier in tiers_order:
                if excess <= 0:
                    break
                if adjusted_sample_counts[tier] > 1:
                    reduction = min(excess, adjusted_sample_counts[tier] - 1)
                    adjusted_sample_counts[tier] -= reduction
                    excess -= reduction
        
        # 如果缩放后总数不足，补充到高优先级层
        elif adjusted_total < max_samples:
            shortage = max_samples - adjusted_total
            tiers_order = ['Top5%', '5%-15%', '15%-35%', '35%-65%', '65%-100%']
            for tier in tiers_order:
                if shortage <= 0:
                    break
                if tier in tier_counts:
                    max_possible = tier_counts[tier] - adjusted_sample_counts[tier]
                    addition = min(shortage, max_possible)
                    adjusted_sample_counts[tier] += addition
                    shortage -= addition
        
        final_sample_counts = adjusted_sample_counts
    else:
        final_sample_counts = initial_sample_counts
    
    final_total = sum(final_sample_counts.values())
    print("最终各层抽样数量：", final_sample_counts)
    print("最终总样本量：", final_total)
    
    # 执行分层抽样
    sampled_dfs = []
    for tier, group in df_sorted.groupby('gvc_tier'):
        if tier in final_sample_counts and final_sample_counts[tier] > 0:
            sample_size = min(final_sample_counts[tier], len(group))
            if sample_size > 0:
                sampled_group = group.sample(n=sample_size, random_state=42)
                sampled_dfs.append(sampled_group)
    
    # 合并抽样结果
    if sampled_dfs:
        sampled_df = pd.concat(sampled_dfs).reset_index(drop=True)
    else:
        sampled_df = pd.DataFrame()
    
    print(f"实际抽样结果数量：{len(sampled_df)}")
    
    return sampled_df


def calculate_gv_ceiling(df: pd.DataFrame, claude_output) -> pd.DataFrame:
    try:
        df['gvc'].fillna(0, inplace=True)
        df['gvc'] = df['gvc'].astype(float)
        df =  df.merge(claude_output, on='asin', how='left')

        df = df[df['in_scope'].isna() == False]
        total_gvc = df['gvc'].sum()
        print(f"Total GVC: {total_gvc}")
        in_scope_gvc = df[df['in_scope'] == 'yes']['gvc'].sum()
        print(f"In-scope GVC: {in_scope_gvc}")
        not_in_scope_gvc = df[df['in_scope'] == 'no']['gvc'].sum()
        print(f"Not in-scope GVC: {not_in_scope_gvc}")
        print(f"Not in-scope length: {len(df[df['in_scope'] == 'no']['gvc'])}")
        gv_ceiling = in_scope_gvc / total_gvc
    except Exception as e:
        print(f"Error calculating GV Ceiling: {e}")
        gv_ceiling = -1
    
    return gv_ceiling
