# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a product compatibility verification system that uses LLMs (primarily Claude models) to analyze Amazon product data and determine whether products are compatible with each other. The system focuses on product pairs like "PRINTER-INKJET_PRINTER_INK" or "CAMERA_DIGITAL-FLASH_MEMORY" to establish compatibility relationships.

## Architecture

**Core Components:**
- `main.py`: Main execution script that orchestrates the entire workflow
- `src/call_llm.py`: LLM client wrapper for AWS Bedrock Claude models with multi-threading support
- `src/build_prompt.py`: Prompt engineering module containing compatibility criteria and prompt templates
- `src/utils.py`: Utility functions for data processing, image handling, and GV ceiling calculations
- `src/constant.py`: Model configurations and mappings

**Data Flow:**
1. Load product data from Excel files (Amazon product catalog)
2. Download and upload product images to S3
3. Generate compatibility verification prompts using product details and images
4. Call Claude models via AWS Bedrock to determine compatibility
5. Calculate GV (Gross Value) ceiling metrics
6. Output results to Excel and JSON files

## Key Features

- **Multi-region AWS Bedrock support**: Dynamically tests and uses multiple AWS regions and accounts
- **Concurrent processing**: Uses ThreadPoolExecutor and ProcessPoolExecutor for parallel execution
- **Image analysis**: Incorporates product images from S3 for enhanced compatibility assessment
- **Stratified sampling**: Implements sophisticated sampling strategies based on product tiers
- **Comprehensive compatibility criteria**: Detailed product category definitions and compatibility rules

## Common Commands

```bash
# Run the main compatibility analysis
python main.py

# The script processes multiple product type pairs and marketplaces automatically
# Output files are saved in data/{datetime}/ directory
```

## Configuration

The system processes predefined product type pairs across multiple marketplaces:
- **Product pairs**: 25 combinations like "PRINTER-INKJET_PRINTER_INK"
- **Marketplaces**: US, UK, DE, FR, CA, IT, ES
- **Models**: Supports Claude 3 Sonnet, 3.5 Haiku, 3.5 Sonnet V2, 3.7 Sonnet, 4 Sonnet, and DeepSeek R1

## AWS Integration

- **AWS Bedrock**: Primary LLM inference service
- **S3**: Image storage and retrieval
- **DynamoDB**: Claude account management
- **Multi-account support**: Automatically discovers and utilizes multiple AWS accounts

## Data Processing

- **Input**: Excel files containing Amazon product data (ASIN, item names, bullet points, GVC)
- **Sampling**: Implements tier-based sampling with configurable ratios
- **Output**: JSON logs and Excel files with compatibility results

## Dependencies

The codebase uses standard Python libraries:
- `pandas`: Data manipulation
- `boto3`: AWS SDK
- `concurrent.futures`: Parallel processing
- `tqdm`: Progress bars
- `json`, `re`, `os`: Standard utilities

## File Structure

```
├── main.py              # Main execution script
├── src/
│   ├── call_llm.py      # LLM client and multi-threading logic
│   ├── build_prompt.py  # Prompt templates and compatibility criteria
│   ├── utils.py         # Utility functions
│   └── constant.py      # Model configurations
└── data/                # Input/output data directory
```

## Important Notes

- The system requires AWS credentials configured with appropriate permissions for Bedrock, S3, and DynamoDB
- Product images must be available in the specified S3 bucket structure
- The compatibility criteria in `build_prompt.py` are comprehensive and product-specific
- Results include both binary compatibility decisions and calculated GV ceiling metrics

## Updated Project: Printer Model Extraction

The project has been extended with a new functionality to extract printer model information from product descriptions:

### New Files:
- `extract_printer_models_main.py`: Main program for printer model extraction
- `demo_extraction.py`: Demo version with configurable record processing
- `test_extraction.py`: Test suite for validation
- `src/extract_printer_models.py`: Prompt templates for printer model extraction
- `src/printer_model_llm.py`: LLM client for printer model extraction

### Usage:
```bash
# Run full extraction
python extract_printer_models_main.py

# Run demo with options
python demo_extraction.py

# Run tests
python test_extraction.py
```

### Output:
- Generates Excel files with original data plus extracted printer model information
- JSON logs containing raw LLM responses
- Statistics on extraction success rates